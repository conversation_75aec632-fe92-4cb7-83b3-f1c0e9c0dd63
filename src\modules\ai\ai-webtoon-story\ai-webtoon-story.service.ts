import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import {
  EAiCutChapterType,
  EAiWebtoonBasicStatus,
  EAiWebtoonChapterStatusGenerateImage,
  EAiWebtoonCutChapterStatusGenerateImage,
} from 'src/common/ai-webtoon.enum';
import { In } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { AiWebtoonCharacterService } from '../ai-webtoon-character/ai-webtoon-character.service';
import { CreateStoryWebtoonDto } from '../ai-webtoon-story/dto/create-story.dto';

import * as moment from 'moment-timezone';
import { DirectionSort } from 'src/common/common.enum';
import { IAiGeneralCharacter } from 'src/common/interfaces/ai-webtoon/ai-webtoon-character.interface';
import { AI_MESSAGE_CONFIG, MESSAGE_CONFIG } from 'src/common/message.config';
import { isValidTimeZone } from 'src/common/utilities/check.utility';
import { AiWebtoonChapterEntity } from 'src/database/entities/ai-webtoon-chapter.entity';
import { AiWebtoonCutChapterEntity } from 'src/database/entities/ai-webtoon-cut-chapter.entity';
import { AiWebtoonSceneChapterEntity } from 'src/database/entities/ai-webtoon-scene-chapter.entity';
import { AiLetteringLanguageRepository } from 'src/database/repositories/ai-lettering-language.repository';
import { AiLetteringRepository } from 'src/database/repositories/ai-lettering.repository';
import { AiWebtoonChapterRepository } from 'src/database/repositories/ai-webtoon-chapter.repository';
import { AiWebtoonCutChapterRepository } from 'src/database/repositories/ai-webtoon-cut-chapter.repository';
import { AiWebtoonSceneChapterRepository } from 'src/database/repositories/ai-webtoon-scene-chapter.repository';
import { AiWebtoonStoryAiLetteringRepository } from 'src/database/repositories/ai-webtoon-story-ai-lettering.repository';
import { AiWebtoonStoryRepository } from 'src/database/repositories/ai-webtoon-story.repository';
import { BorderConfigDto } from '../ai-webtoon-chapter/dto/common-scene-frame.dto';
import { AiWebtoonCutChapterService } from '../ai-webtoon-cut-chapter/ai-webtoon-cut-chapter.service';
import { AddAiLetteringsDto } from './dto/add-ai-letterings.dto';
import { ChooseCharacterDto } from './dto/choose-character.dto';
import { CopyChaptersDto } from './dto/copy-chapters.dto';
import { CreateChapterManualDto } from './dto/create-chapter-manual.dto';
import {
  ListStoryWebtoonDto,
  ListStoryWebtoonHidden,
  ListStoryWebtoonSearchBy,
  ListStoryWebtoonSearchByTime,
} from './dto/list-story.dto';
import { RemoveAiLetteringsDto } from './dto/remove-ai-letterings.dto';
import { RemoveCharacterDto } from './dto/remove-character.dto';
import { UpdateFontConfigDto } from './dto/update-font-config.dto';

@Injectable()
export class AiWebtoonStoryService {
  constructor(
    private readonly aiWebtoonStoryRepository: AiWebtoonStoryRepository,
    private readonly aiWebtoonChapterRepository: AiWebtoonChapterRepository,
    private readonly aiWebtoonCharacterService: AiWebtoonCharacterService,
    private readonly aiWebtoonCutChapterRepository: AiWebtoonCutChapterRepository,
    private readonly aiWebtoonCutChapterService: AiWebtoonCutChapterService,
    private readonly aiWebtoonSceneChapterRepository: AiWebtoonSceneChapterRepository,
    private readonly aiLetteringLanguageRepository: AiLetteringLanguageRepository,
    private readonly aiWebtoonStoryAiLetteringRepository: AiWebtoonStoryAiLetteringRepository,
    private readonly aiLetteringRepository: AiLetteringRepository,
    // private readonly socketService: SocketService,
  ) {}
  private readonly logger = new Logger(AiWebtoonStoryService.name);

  async sendSocketUpdateAllChaptersByStoryId(
    storyId: number,
    withDeleted = false,
  ) {
    const chapters = await this.aiWebtoonChapterRepository.find({
      where: {
        aiWebtoonStoryId: storyId,
      },
      withDeleted,
    });

    chapters.forEach((_chapter) => {
      // this.socketService.emitUpdateChapter(chapter.id);
    });
    return true;
  }

  async checkUpdateStoryStatusGenerateSceneFrameDone(aiWebtoonStoryId: number) {
    const check = await this.aiWebtoonChapterRepository.findOne({
      where: {
        aiWebtoonStoryId,
        status: In([
          EAiWebtoonBasicStatus.GENERATING,
          EAiWebtoonBasicStatus.NOT_GENERATED,
        ]),
      },
    });

    if (!check) {
      await this.aiWebtoonStoryRepository.update(aiWebtoonStoryId, {
        status: EAiWebtoonBasicStatus.GENERATED,
      });
    }
  }

  async create(body: CreateStoryWebtoonDto, adminId: number) {
    const letteringLanguage = await this.aiLetteringLanguageRepository.findOne({
      where: {
        title: 'All',
      },
    });

    if (!letteringLanguage) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_LETTERING_LANGUAGE_NOT_FOUND,
      );
    }

    const result = await this.aiWebtoonStoryRepository.save({
      ...body,
      aiLetteringLanguageId: letteringLanguage.id,
      adminId,
    });
    return { id: result?.id };
  }

  async update(id: number, body: CreateStoryWebtoonDto) {
    const story = await this.aiWebtoonStoryRepository.findOne({
      where: { id },
    });
    if (!story)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_STORY_NOT_FOUND);

    const data = {
      ...story,
      ...body,
    };
    await this.aiWebtoonStoryRepository.save(data);
    return true;
  }

  async updateHidden(id: number) {
    const story = await this.aiWebtoonStoryRepository.findOne({
      where: { id },
    });
    if (!story)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_STORY_NOT_FOUND);

    await this.aiWebtoonStoryRepository.update(id, {
      isHidden: !story.isHidden,
    });
    return true;
  }

  async list(dto: ListStoryWebtoonDto, timezone: string) {
    if (!isValidTimeZone(timezone)) {
      throw new BadRequestException(MESSAGE_CONFIG.TIMEZONE_INVALID);
    }

    const query = this.aiWebtoonStoryRepository
      .createQueryBuilder('aiWtStory')
      .select(['aiWtStory', 'aiWebtoonChapters', 'admin.id', 'admin.username'])
      .addSelect(
        'COALESCE(aiWtStory.latestChapterUpdatedAt, aiWtStory.updatedAt)',
        'lastUpdated',
      )
      .leftJoin('aiWtStory.aiWebtoonChapters', 'aiWebtoonChapters')
      .leftJoin('aiWtStory.admin', 'admin');

    if (dto.hidden && dto.hidden !== ListStoryWebtoonHidden.ALL) {
      query.where('aiWtStory.isHidden = :isHidden', {
        isHidden: dto.hidden === ListStoryWebtoonHidden.HIDE,
      });
    }

    if (dto.search && dto.searchBy) {
      if (dto.searchBy === ListStoryWebtoonSearchBy.TITLE) {
        query.andWhere('aiWtStory.title LIKE :title', {
          title: `%${dto.search}%`,
        });
      } else if (dto.searchBy === ListStoryWebtoonSearchBy.ADMIN_NAME) {
        query.andWhere('admin.username LIKE :username', {
          username: `%${dto.search}%`,
        });
      }
    }

    if (dto.searchByTime && dto.fromTime) {
      const fromTime = moment(dto.fromTime)
        .tz(timezone)
        .startOf('day')
        .utc()
        .format('YYYY-MM-DD HH:mm:ss');

      if (dto.searchByTime === ListStoryWebtoonSearchByTime.CREATED_AT) {
        query.andWhere('aiWtStory.createdAt >= :fromTime', {
          fromTime,
        });
      } else {
        query.andWhere(
          'COALESCE(aiWtStory.latestChapterUpdatedAt, aiWtStory.updatedAt) >= :fromTime',
          {
            fromTime,
          },
        );
      }
    }
    if (dto.searchByTime && dto.toTime) {
      const toTime = moment(dto.toTime)
        .tz(timezone)
        .endOf('day')
        .utc()
        .format('YYYY-MM-DD HH:mm:ss');

      if (dto.searchByTime === ListStoryWebtoonSearchByTime.CREATED_AT) {
        query.andWhere('aiWtStory.createdAt <= :toTime', {
          toTime,
        });
      } else {
        query.andWhere(
          'COALESCE(aiWtStory.latestChapterUpdatedAt, aiWtStory.updatedAt) <= :toTime',
          {
            toTime,
          },
        );
      }
    }

    let orderCol = 'aiWtStory.id';
    let orderDir = DirectionSort.DESC;

    if (dto.sortBy) {
      if (dto.sortBy === ListStoryWebtoonSearchByTime.CREATED_AT) {
        orderCol = 'aiWtStory.createdAt';
      } else if (dto.sortBy === ListStoryWebtoonSearchByTime.UPDATED_AT) {
        orderCol = 'lastUpdated';
      }
    }

    if (dto.direction) {
      orderDir = dto.direction;
    }

    const [items, total] = await query
      .orderBy(orderCol, orderDir)
      .skip((dto.page - 1) * dto.limit)
      .take(dto.limit)
      .getManyAndCount();

    if (items.length) {
      items.forEach((item) => {
        item.numberOfChapters = item.aiWebtoonChapters?.length;
        item.numberOfChaptersDone = item.aiWebtoonChapters?.filter(
          (e) =>
            e.statusGenerateImage === EAiWebtoonChapterStatusGenerateImage.DONE,
        ).length;

        delete item.aiWebtoonChapters;
      });
    }

    return {
      data: items,
      page: dto.page,
      limit: dto.limit,
      totalPages: Math.ceil(total / dto.limit),
      total,
    };
  }

  async detail(id: number) {
    const story = await this.aiWebtoonStoryRepository.findOne({
      where: { id },
    });

    if (!story)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_STORY_NOT_FOUND);

    return story;
  }

  async createChapter(
    aiWebtoonStoryId: number,
    body: CreateChapterManualDto,
    adminId: number,
  ) {
    const [story, checkChapter] = await Promise.all([
      this.detail(aiWebtoonStoryId),
      this.aiWebtoonChapterRepository.findOne({
        where: {
          aiWebtoonStoryId,
          chapter: body.chapter,
        },
      }),
    ]);

    if (checkChapter) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_STORY_CHAPTER_EXISTS);
    }
    const newChapter = this.aiWebtoonChapterRepository.create({
      aiWebtoonStoryId: story.id,
      chapter: body.chapter,
      title: body.title,
      adminId,
    });
    await this.aiWebtoonChapterRepository.save(newChapter);

    return true;
  }

  getUniqueCharactersByCuts(cuts: AiWebtoonCutChapterEntity[]) {
    const allCharacters = cuts.flatMap((e) => e.characters);
    const result = Array.from(new Set(allCharacters.map((char) => char))).map(
      (charUuid) => {
        const char = allCharacters.find((c) => c === charUuid);
        return char;
      },
    );

    return result;
  }

  async chooseCharacter(id: number, body: ChooseCharacterDto) {
    const uniqueCharacters = body.data.filter(
      (item, index, self) =>
        index ===
        self.findIndex(
          (t) =>
            t.character === item.character ||
            t.characterId === item.characterId,
        ),
    );
    if (uniqueCharacters.length !== body.data.length) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_STORY_UNIQUE_CHARACTERS,
      );
    }

    const story = await this.detail(id);
    const generalCharacters = story.characters;

    await Promise.all(
      body.data.map((e) =>
        this.aiWebtoonCharacterService.detail(e.characterId),
      ),
    );

    const characterChanged: IAiGeneralCharacter[] = [];

    generalCharacters.forEach((generalChar) => {
      const match = body.data.find(
        (dataChar) => dataChar.uuid === generalChar.uuid,
      );
      if (match) {
        const isChangeCharacterId =
          match.characterId !== generalChar.characterId;
        const isChangeGender = match.gender !== generalChar.gender;
        const isChangeType = match.type !== generalChar.type;

        generalChar.characterId = match.characterId;
        generalChar.character = match.character;
        generalChar.gender = match.gender;
        generalChar.type = match.type;

        if (isChangeCharacterId || isChangeGender || isChangeType)
          characterChanged.push(generalChar);
      }
    });

    if (characterChanged.length) {
      await this.deleteImagesInChaptersWhenCharactersChange(
        story.id,
        characterChanged,
      );
    }

    const newCharacters: IAiGeneralCharacter[] = body.data
      .filter((e) => !e.uuid)
      .map((e) => ({
        uuid: uuidv4(),
        characterId: e.characterId,
        character: e.character,
        gender: e.gender,
        type: e.type,
      }));

    await Promise.all([
      this.aiWebtoonStoryRepository.update(id, {
        characters: [...generalCharacters, ...newCharacters],
      }),
      this.aiWebtoonCharacterService.addStoryIdUsedForMultipleCharacters(
        body.data.map((e) => e.characterId),
        id,
      ),
    ]);

    await this.sendSocketUpdateAllChaptersByStoryId(id);

    return true;
  }

  async removeCharacter(id: number, body: RemoveCharacterDto) {
    const story = await this.detail(id);
    let generalCharacters = story.characters;

    const characterExists = generalCharacters.find(
      (genChar) => genChar.uuid === body.characterUuid,
    );

    if (!characterExists)
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND);

    await this.checkTheCharacterUsedInTheStory(id, body.characterUuid);

    generalCharacters = generalCharacters.filter(
      (genChar) => genChar.uuid !== body.characterUuid,
    );

    await Promise.all([
      this.aiWebtoonStoryRepository.update(id, {
        characters: generalCharacters,
      }),
      this.aiWebtoonCharacterService.removeStoryIdUsedForCharacter(
        Number(characterExists.characterId),
        id,
      ),
    ]);

    await this.sendSocketUpdateAllChaptersByStoryId(id);
    return true;
  }

  async getStoriesUsedByCharacter(characterId: number) {
    const character = await this.aiWebtoonCharacterService.detail(characterId);

    if (!character.storiesIdUsed.length) return [];

    const stories = await this.aiWebtoonStoryRepository.find({
      where: {
        id: In(character.storiesIdUsed),
      },
      order: {
        id: 'ASC',
      },
    });
    return stories;
  }

  async updateFontConfig(id: number, body: UpdateFontConfigDto) {
    const letteringLanguage = await this.aiLetteringLanguageRepository.findOne({
      where: {
        id: body.aiLetteringLanguageId,
      },
    });

    if (!letteringLanguage) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_LETTERING_LANGUAGE_NOT_FOUND,
      );
    }

    await this.detail(id);
    await this.aiWebtoonStoryRepository.update(id, {
      fontFamily: body.fontFamily,
      fontSize: body.fontSize,
      fontStyle: body.fontStyle,
      fontWeight: body.fontWeight,
      aiLetteringLanguageId: body.aiLetteringLanguageId,
      dialogueColor: body.dialogueColor,
      dialogueFontStyle: body.dialogueFontStyle,
      bubbleFillColor: body.bubbleFillColor,
      bubbleStrokeColor: body.bubbleStrokeColor,
    });
    await this.sendSocketUpdateAllChaptersByStoryId(id);
    return true;
  }

  async updateBorderConfig(id: number, body: BorderConfigDto) {
    await this.detail(id);
    await this.aiWebtoonStoryRepository.update(id, {
      borderType: body.borderType,
      borderColor: body.borderColor,
      borderWeight: body.borderWeight,
    });
    await this.sendSocketUpdateAllChaptersByStoryId(id);
    return true;
  }

  async addAiLetterings(id: number, dto: AddAiLetteringsDto) {
    const story = await this.aiWebtoonStoryRepository.findOne({
      where: { id },
    });
    if (!story) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_STORY_NOT_FOUND);
    }

    const letterings = await this.aiLetteringRepository.find({
      where: { id: In(dto.aiLetteringIds) },
    });

    if (letterings.length !== dto.aiLetteringIds.length) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_LETTERING_NOT_FOUND);
    }

    const existingRelations =
      await this.aiWebtoonStoryAiLetteringRepository.find({
        where: {
          aiWebtoonStoryId: id,
          aiLetteringId: In(dto.aiLetteringIds),
        },
      });

    const newLetteringIds = dto.aiLetteringIds.filter(
      (letteringId) =>
        !existingRelations.some((rel) => rel.aiLetteringId === letteringId),
    );

    if (newLetteringIds.length === 0) {
      return true;
    }

    const relations = newLetteringIds.map((letteringId) => ({
      aiWebtoonStoryId: id,
      aiLetteringId: letteringId,
    }));

    await this.aiWebtoonStoryAiLetteringRepository.save(relations);
    await this.sendSocketUpdateAllChaptersByStoryId(id);
    return true;
  }

  async removeAiLetterings(id: number, dto: RemoveAiLetteringsDto) {
    const story = await this.aiWebtoonStoryRepository.findOne({
      where: { id },
    });
    if (!story) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_STORY_NOT_FOUND);
    }

    await this.aiWebtoonStoryAiLetteringRepository.delete({
      aiWebtoonStoryId: id,
      aiLetteringId: In(dto.aiLetteringIds),
    });

    await this.sendSocketUpdateAllChaptersByStoryId(id);
    return true;
  }

  async checkTheCharacterUsedInTheStory(
    storyId: number,
    characterUuid: string,
  ) {
    const story = await this.aiWebtoonStoryRepository
      .createQueryBuilder('aiWtStory')
      .leftJoinAndSelect('aiWtStory.aiWebtoonChapters', 'aiWebtoonChapters')
      .leftJoinAndSelect('aiWebtoonChapters.scenes', 'scenes')
      .leftJoinAndSelect('scenes.cuts', 'cuts', 'cuts.type = :type', {
        type: EAiCutChapterType.NORMAL,
      })
      .where('aiWtStory.id = :storyId', { storyId })
      .getOne();

    if (story && story.aiWebtoonChapters) {
      story.aiWebtoonChapters.forEach((chapter) => {
        chapter.scenes?.forEach((scene) => {
          scene.cuts.forEach((cut) => {
            const charactersByCut = cut.characters;
            const matchCharacters = charactersByCut.filter(
              (uuid) => uuid === characterUuid,
            );

            if (matchCharacters.length) {
              throw new BadRequestException(
                AI_MESSAGE_CONFIG.AI_STORY_CHARACTER_IN_USE,
              );
            }
          });
        });
      });
    }

    return true;
  }

  async deleteImagesInChaptersWhenCharactersChange(
    storyId: number,
    characterChanged: IAiGeneralCharacter[],
  ) {
    const story = await this.aiWebtoonStoryRepository
      .createQueryBuilder('aiWtStory')
      .leftJoinAndSelect('aiWtStory.aiWebtoonChapters', 'aiWebtoonChapters')
      .leftJoinAndSelect('aiWebtoonChapters.scenes', 'scenes')
      .leftJoinAndSelect('scenes.cuts', 'cuts', 'cuts.type = :type', {
        type: EAiCutChapterType.NORMAL,
      })
      .where('aiWtStory.id = :storyId', { storyId })
      .getOne();

    const p: Promise<any>[] = [];
    const p2: Promise<any>[] = [];

    if (story && story.aiWebtoonChapters) {
      story.aiWebtoonChapters.forEach((chapter) => {
        chapter.scenes?.forEach((scene) => {
          scene.cuts.forEach((cut) => {
            const charactersByCut = cut.characters;
            const matchCharacters = charactersByCut.filter((uuid) =>
              characterChanged.some((changedChar) => changedChar.uuid === uuid),
            );
            const validCharacters = charactersByCut.filter(
              (uuid) =>
                !characterChanged.some(
                  (changedChar) => changedChar.uuid === uuid,
                ),
            );

            if (matchCharacters.length) {
              p.push(
                this.aiWebtoonCutChapterRepository.update(cut.id, {
                  isPreview: false,
                  characters: validCharacters,
                  statusGenerateImage:
                    EAiWebtoonCutChapterStatusGenerateImage.NOT_GENERATED,
                  images: [],
                  imageIndex: 0,
                  totalNumberOfImages: 0,
                }),
              );
            }
          });
        });
        p2.push(
          this.aiWebtoonCutChapterService.updateStatusGenerateImageChapter(
            chapter.id,
          ),
        );
      });
    }

    if (p.length) await Promise.all(p);
    if (p2.length) await Promise.all(p2);
    return true;
  }

  async copyChapters(id: number, body: CopyChaptersDto) {
    const targetStory = await this.detail(id);
    const targetCharacters = targetStory.characters;

    const existingCharacterUUids = new Set(
      targetCharacters.map((char) => char.uuid),
    );

    const chapters = await this.aiWebtoonChapterRepository.find({
      where: { id: In(body.chapterIds) },
    });

    if (chapters.length !== body.chapterIds.length) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_STORY_SOME_CHAPTER_NOT_FOUND,
      );
    }

    const sourceStoryIds = [
      ...new Set(chapters.map((chapter) => chapter.aiWebtoonStoryId)),
    ];
    const sourceStories = await this.aiWebtoonStoryRepository.find({
      where: {
        id: In(sourceStoryIds),
      },
    });

    const sourceCharacters = sourceStories.flatMap((story) => {
      const chars = story.characters;
      return chars;
    });

    const newCharacters = sourceCharacters.filter(
      (char) => !existingCharacterUUids.has(char.uuid),
    );

    await Promise.all([
      this.aiWebtoonStoryRepository.update(targetStory.id, {
        characters: [...targetCharacters, ...newCharacters],
      }),
      this.aiWebtoonCharacterService.addStoryIdUsedForMultipleCharacters(
        [...targetCharacters, ...newCharacters].map((e) =>
          Number(e.characterId),
        ),
        id,
      ),
    ]);

    const results = await Promise.all(
      chapters.map(async (sourceChapter) => {
        const chapterCopy: any = { ...sourceChapter };
        delete chapterCopy.id;
        delete chapterCopy.aiWebtoonStory;
        chapterCopy.aiWebtoonStoryId = targetStory.id;

        const newChapter =
          await this.aiWebtoonChapterRepository.save(chapterCopy);

        const scenes = await this.aiWebtoonSceneChapterRepository.find({
          where: { aiWebtoonChapterId: sourceChapter.id },
          order: { order: 'ASC' },
        });

        for (const sourceScene of scenes) {
          const sceneCopy: any = { ...sourceScene };
          delete sceneCopy.id;
          delete sceneCopy.chapter;
          delete sceneCopy.cuts;
          sceneCopy.aiWebtoonChapterId = newChapter.id;

          const newScene =
            await this.aiWebtoonSceneChapterRepository.save(sceneCopy);

          const cuts = await this.aiWebtoonCutChapterRepository.find({
            where: { aiWebtoonSceneChapterId: sourceScene.id },
            order: { order: 'ASC' },
          });

          for (const sourceCut of cuts) {
            const cutCopy: any = { ...sourceCut };
            delete cutCopy.id;
            delete cutCopy.scene;
            cutCopy.aiWebtoonSceneChapterId = newScene.id;

            await this.aiWebtoonCutChapterRepository.save(cutCopy);
          }
        }

        return newChapter.id;
      }),
    );

    this.logger.log(
      `Copied chapters ${body.chapterIds.join(', ')} to story ${
        targetStory.title
      }`,
    );

    return {
      message: 'Chapters copied successfully',
      copiedChapterIds: results,
    };
  }

  async delete(id: number, adminId: number) {
    const story = await this.aiWebtoonStoryRepository.findOne({
      where: { id },
      relations: ['aiWebtoonChapters'],
    });

    if (!story) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_STORY_NOT_FOUND);
    }

    if (story.adminId !== adminId) {
      throw new UnauthorizedException(MESSAGE_CONFIG.ADMIN_NOT_HAVE_PERMISSION);
    }

    const generatingChapter = story.aiWebtoonChapters?.find(
      (chapter) =>
        chapter.statusGenerateImage ===
        EAiWebtoonChapterStatusGenerateImage.GENERATING,
    );

    if (generatingChapter) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_STORY_CANNOT_DELETE_WHILE_GENERATING,
      );
    }

    await this.aiWebtoonCutChapterRepository
      .createQueryBuilder()
      .softDelete()
      .from(AiWebtoonCutChapterEntity)
      .where(
        'aiWebtoonChapterId IN (SELECT id FROM ai_webtoon_chapter WHERE ai_webtoon_story3_id = :storyId)',
        { storyId: id },
      )
      .execute();

    await this.aiWebtoonSceneChapterRepository
      .createQueryBuilder()
      .softDelete()
      .from(AiWebtoonSceneChapterEntity)
      .where(
        'aiWebtoonChapterId IN (SELECT id FROM ai_webtoon_chapter WHERE ai_webtoon_story3_id = :storyId)',
        { storyId: id },
      )
      .execute();

    await this.aiWebtoonChapterRepository
      .createQueryBuilder()
      .softDelete()
      .from(AiWebtoonChapterEntity)
      .where('aiWebtoonStoryId = :storyId', { storyId: id })
      .execute();

    await this.aiWebtoonStoryRepository.softDelete(id);
    await this.sendSocketUpdateAllChaptersByStoryId(id, true);

    return true;
  }
}
