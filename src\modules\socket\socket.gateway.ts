import { Server, Socket } from 'socket.io';

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';

import { EAiGenPromptByDescriptionType } from 'src/common/ai-webtoon.enum';

@Injectable()
@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class SocketGateway
  implements
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnModuleInit {
  @WebSocketServer() server: Server;
  private readySockets = new Set<string>();
  private logger: Logger = new Logger('SocketGateway');

  constructor() { }

  onModuleInit() {
    this.logger.log('SocketGateway module initialized');
  }

  afterInit(_server: Server) {
    this.logger.log('WebSocket Gateway Initialized');
  }

  handleConnection(client: Socket) {
    this.logger.log(`Client attempting to connect: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    this.readySockets.delete(client.id);
  }

  @SubscribeMessage('ping')
  handlePing(client: Socket, _payload: any): any {
    this.logger.log(`Received ping from ${client.id}`);
    return {
      event: 'pong',
      data: {
        message: 'pong',
        timestamp: new Date().toISOString(),
      },
    };
  }

  @SubscribeMessage('clientReady')
  handleClientReady(
    @ConnectedSocket() client: Socket,
    @MessageBody() _data: any,
  ) {
    this.logger.log(`Client ready: ${client.id}`);
    this.readySockets.add(client.id);
  }

  async emitWithRetry(
    socket: Socket,
    event: string,
    data: any,
    maxRetries = 10,
    timeoutMs = 5000,
  ) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const ackPromise = new Promise<string>((resolve, reject) => {
        let isResolved = false;

        socket
          .timeout(timeoutMs)
          .emit(event, data, (err: any, response: any) => {
            isResolved = true;
            if (err) {
              reject(err instanceof Error ? err : new Error(err.toString())); // Timeout or error
            } else {
              resolve(response);
            }
          });

        // Fallback timeout in case .timeout(...) is not reliable
        setTimeout(() => {
          if (!isResolved) reject(new Error('Timeout'));
        }, timeoutMs + 500);
      });

      try {
        const res = await ackPromise;
        if (res === 'received') {
          this.logger.log(
            `Client acknowledged '${event}' on attempt ${attempt}`,
          );
          return;
        }
      } catch (err) {
        this.logger.warn(
          `Retry ${attempt} for '${event}' failed: ${err.message}`,
        );
      }
    }

    this.logger.error(
      `Failed to deliver '${event}' after ${maxRetries} attempts`,
    );
  }

  async emitUpdateChapter(chapterId: number) {
    const data = { chapterId };
    this.server.emit('updateChapter', data);
    this.logger.log(`Sending updateChapter chapter ${chapterId}`);
    for (const socketId of this.readySockets) {
      const socket = this.server.sockets.sockets.get(socketId);
      if (socket) {
        await this.emitWithRetry(socket, 'updateChapter', data);
      }
    }
  }

  emitSyntheticData(syntheticDataId: number) {
    const data = { syntheticDataId };
    this.server.emit('updateSyntheticData', data);
    this.logger.log(
      `Sending updateSyntheticData syntheticDataId ${syntheticDataId}`,
    );
  }

  emitPromptByDescription(
    cutId: number,
    typePrompt: EAiGenPromptByDescriptionType,
    prompt: string,
  ) {
    const data = { cutId, typePrompt, prompt };
    this.server.emit(`generatePromptByDescription`, data);
    this.logger.log(
      `Emitted generatePromptByDescription event for cut ${cutId}`,
    );
  }

  emitMergePreviewImages(chapterId: number, images: string[]) {
    const data = { chapterId, images };
    this.server.emit('mergePreviewImages', data);
    this.logger.log(
      `Emitted mergePreviewImages event for chapter ${chapterId}`,
    );
  }
}
